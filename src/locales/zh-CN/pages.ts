export default {
	// common
	'pages.common.operation': '操作',
	'pages.common.create': '新建',
	'pages.common.save': '保存',
	'pages.common.delete': '删除',
	'pages.common.edit': '编辑',
	'pages.common.create.success': '创建成功',
	'pages.common.create.failure': '创建失败',
	'pages.common.delete.success': '删除成功',
	'pages.common.delete.failure': '删除失败',
	'pages.common.edit.success': '编辑成功',
	'pages.common.edit.failure': '编辑失败',
	'pages.common.confirm': '确定',
	'pages.common.cancel': '取消',
	'pages.common.submit': '提交',
	'pages.common.reset': '重置',
	'pages.common.search': '搜索',
	'pages.common.refresh': '刷新',
	'pages.common.close': '关闭',
	'pages.common.createdBy': '创建人',
	'pages.common.createdAt': '创建时间',
	'pages.common.lastUpdatedBy': '最后更新人',
	'pages.common.updatedAt': '最后更新时间',
	'pages.common.description': '描述',
	'pages.common.delete.confirm': '确认删除吗？',
	'pages.common.tenantId': '租户ID',
	'pages.common.orderSeq': '排序',

	// login
	'pages.login.slogan': '体验，本应智能',
	'pages.login.sloganTitle': '精准引导，高效增长',
	'pages.login.sloganSubTitle': '让用户每一步，都走得更顺',
	'pages.login.sloganDesc':
		'产品与用户的理性对话，每一步引导，都有数据支撑，从体验到转化全程可控，不打扰，才是最高效的引导',
	'pages.login.title': '登录',
	'pages.login.register.success': '注册成功！',
	'pages.login.register.failure': '注册失败！',
	'pages.login.register.title': '立即注册',
	'pages.login.register.login': '直接登录',
	'pages.login.email.placeholder': '邮箱',
	'pages.login.email.required': '邮箱是必填项！',
	'pages.login.email.error': '邮箱格式不正确！',
	'pages.login.password.placeholder': '请输入密码',
	'pages.login.password.required': '密码是必填项！',
	'pages.login.noAccount': '没有账号？',
	'pages.login.hasAccount': '已有账号？',
	'pages.login.success': '登录成功！',
	'pages.login.failure': '登录失败，请重试！',
	'pages.login.captchaCode.placeholder': '请输入验证码',
	'pages.login.captchaCode.required': '验证码是必填项！',
	'pages.login.captchaCode.success': '验证邮件已发送，请前往注册邮箱查看',

	// user manager
	'pages.user.title': '用户管理',
	'pages.user.model.realName': '用户名',
	'pages.user.model.email': '邮箱',
	'pages.user.model.phone': '手机号码',
	'pages.user.model.phone.error': '手机号码格式错误',

	// tenant manager
	'pages.tenant.title': '租户管理',
	'pages.tenant.model.tenantCode': '租户编码',
	'pages.tenant.model.tenantName': '租户名称',

	// application manager
	'pages.application.title': '应用管理',
	'pages.application.model.applicationCode': '应用编码',
	'pages.application.model.applicationName': '应用名称',

	// guide manager
	'pages.guide.title': '引导管理',
	'pages.guide.model.guideCode': '引导编码',
	'pages.guide.model.guideName': '引导名称',

	// dictionary manager
	'pages.dictionary.title': '数据字典',
	'pages.dictionary.model.dictCode': '字典编码',
	'pages.dictionary.model.dictName': '字典名称',
	'pages.dictionaryItem.model.dictItemCode': '值',
	'pages.dictionaryItem.model.dictItemName': '含义',

	// menu manager
	'pages.menu.model.menuCode': '菜单编码',
	'pages.menu.model.menuName': '菜单名称',
	'pages.menu.model.menuType': '菜单类型',
	'pages.menu.model.menuIcon': '菜单图标',
	'pages.menu.model.menuRoute': '菜单路径',
	'pages.menu.model.orderSeq': '排序',

	// role manager
	'pages.role.title': '角色管理',
	'pages.role.model.roleCode': '角色编码',
	'pages.role.model.roleName': '角色名称',
};
