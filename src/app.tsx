import React from 'react';

import { history, Link } from '@umijs/max';
import type { RunTimeLayoutConfig } from '@umijs/max';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { TeamOutlined, AppstoreOutlined } from '@ant-design/icons';

import defaultSettings from '../config/defaultSettings';
import { AvatarDropdown } from './components/RightContent/AvatarDropdown';
import { requestConfig } from './requestConfig';
import { currentUser as queryCurrentUser, queryApplications } from './services';

const loginPath = '/login';
const registerPath = '/register';

export async function getInitialState(): Promise<{
	settings?: Partial<LayoutSettings>;
	currentUser?: API.CurrentUser;
	loading?: boolean;
	applications?: API.ValueEnum[];
	fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
	fetchApplications?: (tenantId: string) => Promise<API.ValueEnum[] | undefined>;
}> {
	const fetchUserInfo = async () => {
		try {
			const msg = await queryCurrentUser();
			return msg;
		} catch (error) {
			history.push(loginPath);
		}
		return undefined;
	};

	const fetchApplications = async () => {
		try {
			const msg = await queryApplications();
			return msg;
		} catch (error) {
			history.push(loginPath);
		}
		return undefined;
	};

	// 如果不是登录页面，执行
	const { location } = history;
	if (![loginPath, registerPath].includes(location.pathname)) {
		const currentUser = await fetchUserInfo();
		const applications = currentUser?.tenantId ? await fetchApplications() : [];
		return {
			fetchUserInfo,
			currentUser,
			applications: applications,
			settings: defaultSettings as Partial<LayoutSettings>,
		};
	}
	return {
		fetchUserInfo,
		fetchApplications,
		settings: defaultSettings as Partial<LayoutSettings>,
	};
}

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
	return {
		token: {
			bgLayout: '#f3f3f5',
			header: {
				colorBgHeader: '#ffffff',
				heightLayoutHeader: 70,
			},
			sider: {
				colorMenuBackground: '#ffffff',
				colorBgMenuItemHover: 'linear-gradient( 90deg, rgba(39,96,224,0.5) 0%, #2760E0 100%)',
				colorBgMenuItemActive: 'linear-gradient( 90deg, rgba(39,96,224,0.5) 0%, #2760E0 100%)',
				colorBgMenuItemSelected: 'linear-gradient( 90deg, rgba(39,96,224,0.5) 0%, #2760E0 100%)',
			},
			pageContainer: {
				colorBgPageContainer: '#f3f3f5',
				paddingInlinePageContainerContent: 10,
			},
		},
		headerTitleRender: (logo) => <a key="title">{logo}</a>,
		pageTitleRender: false,
		collapsedButtonRender: false,
		footerRender: false,
		breadcrumbRender: false,
		avatarProps: {
			render: (_, avatarChildren) => {
				return <AvatarDropdown menu>{avatarChildren}</AvatarDropdown>;
			},
		},
		onPageChange: () => {
			const { location } = history;
			// 如果没有登录，重定向到 login
			if (!initialState?.currentUser && location.pathname !== loginPath) {
				history.push(loginPath);
			}
		},
		links: [
			<Link key="user-management" to="/user">
				<TeamOutlined />
				<span>成员管理</span>
			</Link>,
			<Link key="application-management" to="/application">
				<AppstoreOutlined />
				<span>应用管理</span>
			</Link>,
		],
		childrenRender: (children) => children,
		...initialState?.settings,
	};
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
	...requestConfig,
};
