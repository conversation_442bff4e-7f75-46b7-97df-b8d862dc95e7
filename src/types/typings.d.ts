declare namespace API {
	type ErrorResponse = {
		traceId?: string;
		failed?: boolean;
		message?: string;
		code?: number;
	};

	type Response<T> = ErrorResponse &
		T & {
			objectVersionNumber: number;
			createdBy: string;
			createdAt: Date;
			lastUpdatedBy: string;
			updatedAt: Date;
		};

	type PageParams = {
		current?: number;
		pageSize?: number;
		page?: number;
		size?: number;
	};

	type PageResult<T> = ErrorResponse & {
		content: T[];
		empty: boolean;
		number: number;
		numberOfElements: number;
		size: number;
		totalElements: number;
		totalPages: number;
	};

	type RegisterParams = ErrorResponse & {
		email: string;
		password: string;
	};

	type LoginParams = ErrorResponse & {
		email: string;
		password: string;
	};

	type LoginResult = ErrorResponse & {
		access_token?: string | undefined;
	};

	type CurrentUser = ErrorResponse & {
		userId: string;
		realName?: string;
		avatar?: string;
		email: string;
		phone?: string;
		language?: string;
		applicationId?: string;
		applicationName?: string;
		tenantId: string;
		admin?: boolean;
		avatar?: string;
	};

	type ValueEnum = ErrorResponse & {
		id: string;
		value: string;
		label: string;
	};
}
