import { createStyles } from 'antd-style';

const useStyles = createStyles(() => {
	return {
		content: {
			width: '12rem',
			maxWidth: '12rem',
			marginInlineStart: '0.8rem',
			marginInlineEnd: '0.8rem',
			textAlign: 'left',
			display: 'flex',
			flexDirection: 'column',
			justifyContent: 'center',
		},
		applicationName: {
			height: '2rem',
			lineHeight: '2rem',
			color: '#333333',
			fontSize: '1.4rem',
			maxWidth: '100%',
			overflow: 'hidden',
			textOverflow: 'ellipsis',
			whiteSpace: 'nowrap',
		},
		avatarName: {
			height: '2rem',
			lineHeight: '2rem',
			color: '#999999',
			fontSize: '1.2rem',
		},
		appMenus: {
			width: '12rem',
			backgroundColor: '#2760E0',
		},
	};
});

export default useStyles;