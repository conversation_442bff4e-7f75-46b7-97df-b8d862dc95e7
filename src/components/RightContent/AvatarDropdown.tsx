import { Spin, Avatar, Dropdown } from 'antd';
import { stringify } from 'querystring';
import React, { useCallback } from 'react';
import { flushSync } from 'react-dom';
import { history, useModel } from '@umijs/max';
import { LogoutOutlined, UserOutlined, DownOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useEmotionCss } from '@ant-design/use-emotion-css';

import { useUserInfo } from '@/hooks';
import { logout } from '@/services';
import { removeAccessToken } from '@/utils/cookie';


import useStyles from './style';

export type GlobalHeaderRightProps = {
	menu?: boolean;
	children?: React.ReactNode;
};

export const AvatarName = ({ className }: { className: string }) => {
	const currentUser = useUserInfo();
	return <div className={className}>{currentUser?.realName || currentUser?.email}</div>;
};

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = () => {
	const { styles } = useStyles();
	/**
	 * 退出登录，并且将当前的 url 保存
	 */
	const loginOut = async () => {
		await logout();
		const { search, pathname } = window.location;
		const urlParams = new URL(window.location.href).searchParams;
		/** 此方法会跳转到 redirect 参数所在的位置 */
		const redirect = urlParams.get('redirect');
		// Note: There may be security issues, please note
		if (window.location.pathname !== '/login' && !redirect) {
			removeAccessToken();
			history.replace({
				pathname: '/login',
				search: stringify({
					redirect: pathname + search,
				}),
			});
		}
	};
	const actionClassName = useEmotionCss(({ token }) => {
		return {
			display: 'flex',
			height: '48px',
			marginLeft: 'auto',
			overflow: 'hidden',
			alignItems: 'center',
			padding: '0 8px',
			cursor: 'pointer',
			borderRadius: token.borderRadius,
			'&:hover': {
				backgroundColor: token.colorBgTextHover,
			},
		};
	});
	const { initialState, setInitialState } = useModel('@@initialState');

	const onMenuClick = useCallback(
		(event: any) => {
			const { key } = event;
			if (key === 'logout') {
				flushSync(() => {
					setInitialState((s) => ({ ...s, currentUser: undefined }));
				});
				loginOut();
				return;
			}
			history.push(`/${key}`);
		},
		[setInitialState],
	);

	const loading = (
		<span className={actionClassName}>
			<Spin
				size="small"
				style={{
					marginLeft: 8,
					marginRight: 8,
				}}
			/>
		</span>
	);

	if (!initialState?.currentUser) {
		return loading;
	}

	const { currentUser, applications } = initialState;

	if (!currentUser || !currentUser.realName) {
		return loading;
	}

	const menuItems = [
		{
			key: 'app',
			icon: <AppstoreOutlined />,
			label: '应用管理',
			classNames: styles.appMenus,
			popupStyle: {
				minWidth: '12rem',
			},
			children: applications?.map((app) => ({
				key: app.value,
				label: app.label,
			})),
		},
		{
			key: 'profile',
			icon: <UserOutlined />,
			label: '个人中心',
		},
		{
			type: 'divider' as const,
		},
		{
			key: 'logout',
			icon: <LogoutOutlined />,
			label: '退出登录',
		},
	];

	return (
		<Dropdown
			menu={{
				selectedKeys: [],
				items: menuItems,
				onClick: onMenuClick,
			}}
			trigger={['click']}
		>
			<div>
				{currentUser?.avatar ? (
					<Avatar src={currentUser.avatar} />
				) : (
					<Avatar>{currentUser.realName?.slice(0, 1)}</Avatar>
				)}
				<div className={styles.content}>
					<div className={styles.applicationName}>{currentUser.applicationName || '--'}</div>
					<AvatarName className={styles.avatarName} />
				</div>
				<DownOutlined />
			</div>
		</Dropdown>
	);
};
