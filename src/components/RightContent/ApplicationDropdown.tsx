import { Select } from 'antd';
import { useModel } from '@umijs/max';
import { updateUserApp } from './service';

const ApplicationDropdown = () => {
	const { initialState } = useModel('@@initialState');
	const { currentUser, applications } = initialState || {};
	const { applicationId } = currentUser || {};

	const handleChange = async (value: string) => {
		if (value !== applicationId) {
			await updateUserApp(value);
			window.location.reload();
		}
	};

	return (
		<Select
			defaultValue={applicationId}
			variant="filled"
			style={{ padding: 0, minWidth: 100 }}
			popupMatchSelectWidth
			showSearch
			options={applications}
			onChange={handleChange}
		/>
	);
};

export default ApplicationDropdown;
