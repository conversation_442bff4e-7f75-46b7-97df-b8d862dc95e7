import React from 'react';
import { Button } from 'antd';
import type { ButtonProps } from 'antd';
import useStyles from './style';

interface CButtonProps extends ButtonProps {
	colorful?: boolean;
}

const CButton: React.FC<CButtonProps> = (props) => {
	const { children, colorful = false, ...rest } = props;
	const { styles, cx } = useStyles();

	return (
    <Button
      {...rest}
      className={cx({
        [styles.colorful]: colorful,
      })}
    >
      {children}
    </Button>
  );
};

export default CButton;
