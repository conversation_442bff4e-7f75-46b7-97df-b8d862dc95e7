import { useRef, useState } from 'react';
import { message, Popconfirm, Form, Row, Col } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
	DrawerForm,
	ModalForm,
	PageContainer,
	ProColumns,
	ProFormText,
	ProTable,
	ActionType,
	ProFormSelect,
	ProFormList,
	FormListActionType,
	LightFilter,
} from '@ant-design/pro-components';
import { useIntl, useModel } from '@umijs/max';

import Button from '@/components/Button';
import { defaultPagination, generateTableFetch } from '@/utils/request';
import { DEFAULT_FORM_LAYOUT } from '@/utils/constants';
import { page, createUser, deleteUser, queryUser, getTenants, inviteUsers } from './service';
import { useUserInfo } from '@/hooks';
import useStyles from './style';

const User: React.FC = () => {
	const intl = useIntl();
	const tableRef = useRef<ActionType>();
	const [formSearch, setFormSearch] = useState<Record<string, any>>();
	const inviteRef = useRef<FormListActionType<{ email: string; role: string }>>();
	const [editForm] = Form.useForm<UserType>();
	const userInfo = useUserInfo();
	const { styles } = useStyles();
	const { roles } = useModel('User.model');

	const handleDelete = async (id: string) => {
		const res = await deleteUser(id);
		if (!res?.failed) {
			message.success(intl.formatMessage({ id: 'pages.common.delete.success', defaultMessage: '删除成功' }));
			await tableRef.current?.reload();
		}
	};

	const renderModal = (id: string) => {
		return (
			<DrawerForm<UserType>
				title={intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })}
				trigger={<a key="edit">{intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })}</a>}
				layout="horizontal"
				labelAlign="left"
				{...DEFAULT_FORM_LAYOUT}
				drawerProps={{
					destroyOnClose: true,
				}}
				form={editForm}
				request={async () => {
					const res = await queryUser(id!);
					return res;
				}}
				onFinish={async (values) => {
					const res = await createUser(values);
					if (!res?.failed) {
						message.success(intl.formatMessage({ id: 'pages.common.update.success', defaultMessage: '更新成功' }));
						await tableRef.current?.reload();
						return true;
					}
				}}
			>
				<Form.Item hidden>
					<ProFormText name="userId" />
				</Form.Item>
				<ProFormText
					name="realName"
					label={intl.formatMessage({ id: 'pages.user.model.realName', defaultMessage: '用户名' })}
					rules={[
						{
							required: true,
						},
					]}
				/>
				<ProFormText
					name="email"
					label={intl.formatMessage({ id: 'pages.user.model.email', defaultMessage: '邮箱' })}
					rules={[
						{
							type: 'email',
							message: intl.formatMessage({
								id: 'pages.user.model.email.error',
								defaultMessage: '邮箱格式错误',
							}),
						},
						{
							required: true,
						},
					]}
				/>
				{/* {!isEdit && (
					<ProFormText.Password
						name='password'
						label={intl.formatMessage({ id: 'pages.user.model.password', defaultMessage: '密码' })}
						tooltip={intl.formatMessage({
							id: 'pages.user.model.password.tooltip',
							defaultMessage: '不输入密码则使用默认密码',
						})}
					/>
				)} */}
				<ProFormText
					name="phone"
					label={intl.formatMessage({ id: 'pages.user.model.phone', defaultMessage: '手机号码' })}
					rules={[
						{
							pattern: /^1[3-9]\d{9}$/,
							message: intl.formatMessage({
								id: 'pages.user.model.phone.error',
								defaultMessage: '手机号码格式错误',
							}),
						},
					]}
				/>
				<ProFormSelect
					name="language"
					label={intl.formatMessage({ id: 'pages.user.model.language', defaultMessage: '语言' })}
					options={[
						{
							label: '中文',
							value: 'zh-CN',
						},
						{
							label: '英文',
							value: 'en-US',
						},
					]}
				/>
				<ProFormSelect
					name="tenantId"
					label={intl.formatMessage({ id: 'pages.user.model.tenant', defaultMessage: '所属租户' })}
					hidden={!userInfo?.admin}
					rules={[
						{
							required: userInfo?.admin,
						},
					]}
					request={async () => {
						const res = await getTenants();
						return res;
					}}
				/>
			</DrawerForm>
		);
	};

	const columns: ProColumns<UserType>[] = [
		{
			dataIndex: 'userId',
			hideInTable: true,
			search: false,
		},
		{
			title: intl.formatMessage({ id: 'pages.user.model.realName', defaultMessage: '用户名' }),
			dataIndex: 'realName',
			sorter: true,
		},
		{
			title: intl.formatMessage({ id: 'pages.user.model.email', defaultMessage: '邮箱' }),
			dataIndex: 'email',
			sorter: true,
		},
		{
			title: intl.formatMessage({ id: 'pages.user.model.phone', defaultMessage: '手机号码' }),
			dataIndex: 'phone',
		},
		{
			title: intl.formatMessage({ id: 'pages.common.createdAt', defaultMessage: '创建时间' }),
			dataIndex: 'createdAt',
			hideInSearch: true,
		},
		{
			title: intl.formatMessage({ id: 'pages.common.createdAt', defaultMessage: '创建时间' }),
			dataIndex: 'createdAt',
			valueType: 'dateTimeRange',
			hideInTable: true,
			search: {
				transform: (value) => {
					return {
						startCreatedAt: value[0],
						endCreatedAt: value[1],
					};
				},
			},
		},
		{
			title: intl.formatMessage({ id: 'pages.common.operation', defaultMessage: '操作' }),
			valueType: 'option',
			key: 'operation',
			render: (_, record) => [
				renderModal(record.userId),
				<Popconfirm
					key="delete"
					title={intl.formatMessage({
						id: 'pages.common.delete.confirm',
						defaultMessage: '确认删除吗？',
					})}
					onConfirm={() => handleDelete(record.userId)}
				>
					<a key="delete">{intl.formatMessage({ id: 'pages.common.delete', defaultMessage: '删除' })}</a>
				</Popconfirm>,
			],
		},
	];

	const inviteModal = () => {
		return (
			<ModalForm
				title={intl.formatMessage({ id: 'pages.user.button.invite', defaultMessage: '邀请' })}
				trigger={
					<Button colorful icon={<PlusOutlined />}>
						{intl.formatMessage({ id: 'pages.user.button.invite', defaultMessage: '邀请' })}
					</Button>
				}
				modalProps={{
					destroyOnClose: true,
				}}
				onFinish={async (values) => {
					const users = inviteRef.current?.getList();
					if (users) {
						await inviteUsers(users);
					}
				}}
				grid
			>
				<ProFormList
					name="inviteUsers"
					initialValue={[{ email: '', role: '' }]}
					creatorButtonProps={{
						creatorButtonText: '添加人员',
						block: false,
					}}
					min={1}
					copyIconProps={false}
					actionRef={inviteRef}
				>
					<Row gutter={24}>
						<Col span={12}>
							<ProFormText
								name="email"
								label={intl.formatMessage({ id: 'pages.user.model.email', defaultMessage: '邮箱' })}
								rules={[
									{
										required: true,
									},
									{
										type: 'email',
										message: intl.formatMessage({
											id: 'pages.user.model.email.error',
											defaultMessage: '邮箱格式错误',
										}),
									},
								]}
							/>
						</Col>
						<Col span={12}>
							<ProFormSelect
								name='role'
								label={intl.formatMessage({ id: 'pages.user.model.role', defaultMessage: '角色' })}
								rules={[
									{
										required: true,
									},
								]}
								allowClear={false}
								options={roles as API.ValueEnum[]}
								// request={async () => {
								// 	const res = await findAllRole();
								// 	return res;
								// }}
							/>
						</Col>
					</Row>
				</ProFormList>
			</ModalForm>
		);
	};

	return (
		<PageContainer>
			<ProTable<UserType, API.PageParams>
				actionRef={tableRef}
				rowKey="userId"
				columns={columns}
				params={formSearch}
				pagination={defaultPagination}
				request={async (params, sort) => generateTableFetch(page, params, sort)}
				options={false}
				searchFormRender={() => {
					return (
						<LightFilter
							className={styles.lightFilter}
							footerRender={false}
							onInit={() => {
								setTimeout(() => {
									setFormSearch({
										_timestamp: Date.now(),
									});
								}, 50);
							}}
							onFinish={(values) => {
								setFormSearch(values);
							}}
						>
							<ProFormText
								name="realName"
								label={intl.formatMessage({ id: 'pages.user.model.realName', defaultMessage: '用户名' })}
							/>
							<ProFormText
								name="email"
								label={intl.formatMessage({ id: 'pages.user.model.email', defaultMessage: '邮箱' })}
							/>
							{inviteModal()}
						</LightFilter>
					);
				}}
			/>
		</PageContainer>
	);
};

export default User;
