import { request } from '@umijs/max';

export async function page(
	params: UserType & API.PageParams,
	sort?: { [key: string]: any },
	options?: { [key: string]: any },
): Promise<API.PageResult<UserType>> {
	return request<API.PageResult<UserType>>('/api/v1/user/paging', {
		method: 'GET',
		params: {
			...params,
			...sort,
		},
		...(options || {}),
	});
}

export async function createUser(data: UserType): Promise<API.Response<UserType>> {
	return request<API.Response<UserType>>('/api/v1/user', {
		method: 'POST',
		data,
	});
}

export async function updateUser(data: UserType): Promise<API.Response<UserType>> {
	return request<API.Response<UserType>>('/api/v1/user', {
		method: 'PUT',
		data,
	});
}

export async function deleteUser(id: string): Promise<API.Response<UserType>> {
	return request<API.Response<UserType>>(`/api/v1/user/${id}`, {
		method: 'DELETE',
	});
}

export async function queryUser(id: string): Promise<API.Response<UserType>> {
	return request<API.Response<UserType>>(`/api/v1/user/${id}`, {
		method: 'GET',
	});
}

export async function getTenants(): Promise<API.Response<TenantType[]>> {
	return request<API.Response<TenantType[]>>('/api/v1/tenant/all', {
		method: 'GET',
	});
}

export async function getRoles(): Promise<API.Response<RoleType[]>> {
	return request<API.Response<RoleType[]>>('/api/v1/role/list', {
		method: 'GET',
	});
}

export async function inviteUsers(data: any): Promise<any> {
	return request<any>('/api/v1/user/invite', {
		method: 'POST',
		data,
	});
}
