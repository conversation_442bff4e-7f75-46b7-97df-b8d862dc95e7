import React, { useState, useRef } from 'react';
import { Button, message, Popconfirm, Form } from 'antd';
import { CalendarOutlined } from '@ant-design/icons';
import { useIntl, history } from '@umijs/max';
import {
	PageContainer,
	ProColumns,
	ProTable,
	ModalForm,
	DrawerForm,
	ProFormText,
	ActionType,
	LightFilter,
	ProFormDateRangePicker,
} from '@ant-design/pro-components';
import omit from 'lodash/omit';

import { defaultPagination, generateTableFetch } from '@/utils/request';
import { DEFAULT_FORM_LAYOUT } from '@/utils/constants';
import { rangePresets } from '@/utils/common';

import { page, createGuide, deleteGuide, updateGuide, queryGuide } from './service';

const Gudie: React.FC = () => {
	const [formSearch, setFormSearch] = useState<Record<string, any>>();
	const tableRef = useRef<ActionType>();
	const intl = useIntl();

	const handleDelete = async (id: string) => {
		const res = await deleteGuide(id);
		if (!res?.failed) {
			message.success(intl.formatMessage({ id: 'pages.common.delete.success', defaultMessage: '删除成功' }));
			await tableRef.current?.reload();
		}
	};

	const renderModal = (isEdit = false, id?: string) => {
		const CustomModal = isEdit ? DrawerForm : ModalForm;
		return (
			<CustomModal<GudieType>
				title={
					isEdit
						? intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })
						: intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })
				}
				trigger={
					isEdit ? (
						<a key="edit">{intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })}</a>
					) : (
						<Button type="primary">{intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })}</Button>
					)
				}
				layout="horizontal"
				labelAlign="left"
				{...DEFAULT_FORM_LAYOUT}
				modalProps={{
					destroyOnClose: true,
				}}
				drawerProps={{
					destroyOnClose: true,
				}}
				request={
					isEdit
						? async () => {
								const res = await queryGuide(id!);
								return res;
							}
						: undefined
				}
				onFinish={async (values) => {
					const res = isEdit ? await updateGuide(values) : await createGuide(values);
					if (!res?.failed) {
						message.success(
							isEdit
								? intl.formatMessage({
										id: 'pages.common.update.success',
										defaultMessage: '更新成功',
									})
								: intl.formatMessage({
										id: 'pages.common.create.success',
										defaultMessage: '创建成功',
									}),
						);
						await tableRef.current?.reload();
						return true;
					}
				}}
			>
				<Form.Item hidden>
					<ProFormText name="guideId" />
				</Form.Item>
				<ProFormText
					name="guideCode"
					label={intl.formatMessage({
						id: 'pages.guide.model.guideCode',
						defaultMessage: '引导编码',
					})}
					disabled={isEdit}
					required
				/>
				<ProFormText
					name="guideName"
					label={intl.formatMessage({
						id: 'pages.guide.model.guideName',
						defaultMessage: '引导名称',
					})}
					required
				/>
			</CustomModal>
		);
	};

	const handleDetail = (record: GudieType) => {
		history.push(`/guide/${record.guideId}`, {
			name: record.guideName,
		});
	};

	const columns: ProColumns<GudieType>[] = [
		{
			dataIndex: 'guideId',
			hidden: true,
			search: false,
		},
		{
			title: intl.formatMessage({ id: 'pages.guide.model.guideCode', defaultMessage: '引导编码' }),
			dataIndex: 'guideCode',
			sorter: true,
			// search: false,
			render: (text, record) => [
				<a key="detail" onClick={() => handleDetail(record)}>
					{text}
				</a>,
			],
		},
		{
			title: intl.formatMessage({ id: 'pages.guide.model.guideName', defaultMessage: '引导名称' }),
			dataIndex: 'guideName',
			sorter: true,
			search: false,
		},
		{
			title: intl.formatMessage({ id: 'pages.common.createdAt', defaultMessage: '创建时间' }),
			dataIndex: 'createdAt',
			valueType: 'dateRange',
			// search: false,
		},
		{
			title: intl.formatMessage({ id: 'pages.common.operation', defaultMessage: '操作' }),
			valueType: 'option',
			key: 'operation',
			render: (text, record) => [
				renderModal(true, record.guideId),
				<Popconfirm
					key="delete"
					title={intl.formatMessage({
						id: 'pages.common.delete.confirm',
						defaultMessage: '确认删除吗？',
					})}
					onConfirm={() => handleDelete(record.guideId)}
				>
					<a key="delete">{intl.formatMessage({ id: 'pages.common.delete', defaultMessage: '删除' })}</a>
				</Popconfirm>,
			],
		},
	];

	return (
		<PageContainer>
			<ProTable<GudieType, API.PageParams>
				actionRef={tableRef}
				rowKey="guideId"
				columns={columns}
				params={formSearch}
				pagination={defaultPagination}
				request={async (params, sort) => generateTableFetch(page, params, sort)}
				options={false}
				searchFormRender={() => {
					return (
						<LightFilter
							// className={styles.lightFilter}
							footerRender={false}
							onInit={() => {
								setTimeout(() => {
									setFormSearch({
										_timestamp: Date.now(),
									});
								}, 50);
							}}
							onFinish={(values) => {
								tableRef.current?.reload();
								setFormSearch({
									startCreatedAt: values?.createdRang?.[0],
									endCreatedAt: values?.createdRang?.[1],
									...omit(values, ['createdRang']),
								});
							}}
						>
							<ProFormText
								name="applicationCode"
								label={intl.formatMessage({
									id: 'pages.application.model.applicationCode',
									defaultMessage: '应用编码',
								})}
							/>
							<ProFormText
								name="applicationName"
								label={intl.formatMessage({
									id: 'pages.application.model.applicationName',
									defaultMessage: '应用名称',
								})}
							/>
							<ProFormDateRangePicker
								label={<CalendarOutlined />}
								name="createdRang"
								fieldProps={{ presets: rangePresets }}
							/>
							<Button type="primary">
								{intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })}
							</Button>
						</LightFilter>
					);
				}}
			/>
		</PageContainer>
	);
};

export default Gudie;
