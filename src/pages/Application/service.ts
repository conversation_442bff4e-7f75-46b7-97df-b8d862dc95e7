import { request } from '@umijs/max';

export async function page(
	params: ApplicationType & API.PageParams,
	sort?: { [key: string]: any },
	options?: { [key: string]: any },
): Promise<API.PageResult<ApplicationType>> {
	return request<API.PageResult<ApplicationType>>('/api/v1/application/paging', {
		method: 'GET',
		params: {
			...params,
			...sort,
		},
		...(options || {}),
	});
}

export async function createApplication(data: ApplicationType): Promise<API.Response<ApplicationType>> {
	return request<API.Response<ApplicationType>>('/api/v1/application', {
		method: 'POST',
		data,
	});
}

export async function deleteApplication(id: string): Promise<API.Response<ApplicationType>> {
	return request<API.Response<ApplicationType>>(`/api/v1/application/${id}`, {
		method: 'DELETE',
	});
}

export async function updateApplication(data: ApplicationType): Promise<API.Response<ApplicationType>> {
	return request<API.Response<ApplicationType>>('/api/v1/application', {
		method: 'PUT',
		data,
	});
}

export async function queryApplication(id: string): Promise<API.Response<ApplicationType>> {
	return request<API.Response<ApplicationType>>(`/api/v1/application/${id}`, {
		method: 'GET',
	});
}
