import { useRef, useState } from 'react';
import { message, Popconfirm, Form } from 'antd';
import { CalendarOutlined, PlusOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import {
	PageContainer,
	ProColumns,
	ProTable,
	ModalForm,
	DrawerForm,
	ProFormText,
	ActionType,
	LightFilter,
	ProFormDateRangePicker,
} from '@ant-design/pro-components';
import omit from 'lodash/omit';

import Button from '@/components/Button';
import { defaultPagination, generateTableFetch } from '@/utils/request';
import { useUserInfo } from '@/hooks';
import { DEFAULT_FORM_LAYOUT } from '@/utils/constants';
import { rangePresets } from '@/utils/common';

import { page, createApplication, deleteApplication, updateApplication, queryApplication } from './service';
import useStyles from './style';

const Application: React.FC = () => {
	const [formSearch, setFormSearch] = useState<Record<string, any>>();
	const tableRef = useRef<ActionType>();
	const intl = useIntl();
	const currentUser = useUserInfo();
	const { styles } = useStyles();
	const handleDelete = async (id: string) => {
		const res = await deleteApplication(id);
		if (!res?.failed) {
			message.success(intl.formatMessage({ id: 'pages.common.delete.success', defaultMessage: '删除成功' }));
			if (currentUser?.applicationId === id) {
				window.location.reload();
			} else {
				await tableRef.current?.reload();
			}
		}
	};

	const renderModal = (isEdit = false, id?: string) => {
		const CustomModal = isEdit ? DrawerForm : ModalForm;
		return (
			<CustomModal<ApplicationType>
				title={
					isEdit
						? intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })
						: intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })
				}
				trigger={
					isEdit ? (
						<a key="edit">{intl.formatMessage({ id: 'pages.common.edit', defaultMessage: '编辑' })}</a>
					) : (
						<Button type="primary">{intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })}</Button>
					)
				}
				layout="horizontal"
				labelAlign="left"
				{...DEFAULT_FORM_LAYOUT}
				modalProps={{
					destroyOnClose: true,
				}}
				drawerProps={{
					destroyOnClose: true,
				}}
				request={
					isEdit
						? async () => {
								const res = await queryApplication(id!);
								return res;
							}
						: undefined
				}
				onFinish={async (values) => {
					const res = isEdit ? await updateApplication(values) : await createApplication(values);
					if (!res?.failed) {
						message.success(
							isEdit
								? intl.formatMessage({
										id: 'pages.common.update.success',
										defaultMessage: '更新成功',
									})
								: intl.formatMessage({
										id: 'pages.common.create.success',
										defaultMessage: '创建成功',
									}),
						);
						if (tableRef.current?.pageInfo?.total === 0) {
							window.location.reload();
						} else {
							await tableRef.current?.reload();
						}
						return true;
					}
				}}
			>
				<Form.Item hidden>
					<ProFormText name="applicationId" />
				</Form.Item>
				<ProFormText
					name="applicationCode"
					label={intl.formatMessage({
						id: 'pages.application.model.applicationCode',
						defaultMessage: '应用编码',
					})}
					disabled={isEdit}
					required
				/>
				<ProFormText
					name="applicationName"
					label={intl.formatMessage({
						id: 'pages.application.model.applicationName',
						defaultMessage: '应用名称',
					})}
					required
				/>
			</CustomModal>
		);
	};

	const columns: ProColumns<ApplicationType>[] = [
		{
			dataIndex: 'applicationId',
			hidden: true,
			search: false,
		},
		{
			title: intl.formatMessage({
				id: 'pages.application.model.applicationCode',
				defaultMessage: '应用编码',
			}),
			dataIndex: 'applicationCode',
			sorter: true,
		},
		{
			title: intl.formatMessage({
				id: 'pages.application.model.applicationName',
				defaultMessage: '应用名称',
			}),
			dataIndex: 'applicationName',
			sorter: true,
		},
		{
			title: intl.formatMessage({
				id: 'pages.application.model.tenant',
				defaultMessage: '所属租户',
			}),
			dataIndex: 'tenantName',
			hidden: !currentUser?.admin,
		},
		{
			title: intl.formatMessage({ id: 'pages.common.createdAt', defaultMessage: '创建时间' }),
			dataIndex: 'createdAt',
			valueType: 'dateTime',
		},
		{
			title: intl.formatMessage({ id: 'pages.common.operation', defaultMessage: '操作' }),
			valueType: 'option',
			key: 'operation',
			render: (text, record) => [
				renderModal(true, record.applicationId),
				<Popconfirm
					key="delete"
					title={intl.formatMessage({
						id: 'pages.common.delete.confirm',
						defaultMessage: '确认删除吗？',
					})}
					onConfirm={() => handleDelete(record.applicationId)}
				>
					<a key="delete">{intl.formatMessage({ id: 'pages.common.delete', defaultMessage: '删除' })}</a>
				</Popconfirm>,
			],
		},
	];

	return (
		<PageContainer>
			<ProTable<ApplicationType, API.PageParams>
				actionRef={tableRef}
				rowKey="applicationId"
				columns={columns}
				params={formSearch}
				pagination={defaultPagination}
				request={async (params, sort) => generateTableFetch(page, params, sort)}
				options={false}
				searchFormRender={() => {
					return (
						<>
							<LightFilter
								className={styles.lightFilter}
								footerRender={false}
								onInit={() => {
									setTimeout(() => {
										setFormSearch({
											_timestamp: Date.now(),
										});
									}, 50);
								}}
								onFinish={(values) => {
									tableRef.current?.reload();
									setFormSearch({
										startCreatedAt: values?.createdRang?.[0],
										endCreatedAt: values?.createdRang?.[1],
										...omit(values, ['createdRang']),
									});
								}}
							>
								<ProFormText
									name="applicationCode"
									label={intl.formatMessage({
										id: 'pages.application.model.applicationCode',
										defaultMessage: '应用编码',
									})}
								/>
								<ProFormText
									name="applicationName"
									label={intl.formatMessage({
										id: 'pages.application.model.applicationName',
										defaultMessage: '应用名称',
									})}
								/>
								<ProFormDateRangePicker
									label={<CalendarOutlined />}
									name="createdRang"
									fieldProps={{ presets: rangePresets }}
								/>
								<Button colorful icon={<PlusOutlined />}>
									{intl.formatMessage({ id: 'pages.common.create', defaultMessage: '新建' })}
								</Button>
							</LightFilter>
						</>
					);
				}}
			/>
		</PageContainer>
	);
};

export default Application;
