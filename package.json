{"name": "flowez-app", "version": "1.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@ant-design/use-emotion-css": "1.0.4", "@umijs/route-utils": "^4.0.1", "antd": "^5.24.8", "antd-img-crop": "^4.24.0", "antd-style": "^3.7.1", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "omit.js": "^2.0.2", "querystring": "^0.2.1", "rc-util": "^5.44.4", "react": "^18.3.1", "react-dom": "^18.3.1", "universal-cookie": "^7.2.2"}, "devDependencies": {"@testing-library/react": "^13.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/classnames": "^2.3.4", "@types/history": "^4.7.11", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/lodash.debounce": "^4.0.9", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.4.11", "@umijs/max": "^4.4.11", "cross-env": "^7.0.3", "eslint": "^8.57.1", "gh-pages": "^3.2.3", "husky": "^7.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.5.10", "prettier-plugin-two-style-order": "^1.0.1", "react-dev-inspector": "^1.9.0", "swagger-ui-dist": "^4.19.1", "ts-node": "^10.9.2", "typescript": "^4.9.5", "umi-presets-pro": "^2.0.3"}, "engines": {"node": ">=12.0.0"}}